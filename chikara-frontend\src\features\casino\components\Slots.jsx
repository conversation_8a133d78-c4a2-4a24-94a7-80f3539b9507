import Button from "@/components/Buttons/Button";
import { APIROUTES } from "@/helpers/apiRoutes";
import { api } from "@/helpers/api";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useWindowDimensions from "@/hooks/useWindowDimensions";
import { cn } from "@/lib/utils";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useLayoutEffect, useRef, useState, useEffect } from "react";
import toast from "react-hot-toast";
import SlotCounter from "react-slot-counter";
// Uncomment the line below to use the custom hook approach:
// import { useSlotMachine } from "../hooks/useCasinoOperations";

function generateRandomUniqueImages(max) {
    const uniqueImages = new Set();

    while (uniqueImages.size < 3) {
        const randomNumber = Math.floor(Math.random() * (max - 1 + 1)) + 1;
        uniqueImages.add("image" + randomNumber);
    }

    return Array.from(uniqueImages);
}

// image1 = 2x
// image2 = 3x
// image4 = 4x

const images = {
    image1: "https://d13cmcqz8qkryo.cloudfront.net/static/items/consumables/burger.png",
    image2: "https://d13cmcqz8qkryo.cloudfront.net/static/items/consumables/donut.png",
    image3: "https://d13cmcqz8qkryo.cloudfront.net/static/items/consumables/cola.png",
    image4: "https://d13cmcqz8qkryo.cloudfront.net/static/items/consumables/cabbage.png",
    image5: "https://d13cmcqz8qkryo.cloudfront.net/static/items/consumables/chicken.png",
    image6: "https://d13cmcqz8qkryo.cloudfront.net/static/items/consumables/egg.png",
};

const dummyCharacters = [
    <img key="dummyImg1" className="slotImage size-28 scale-75 md:size-40 md:scale-[.65]" src={images.image1} />,
    <img key="dummyImg2" className="slotImage size-28 scale-75 md:size-40 md:scale-[.65]" src={images.image2} />,
    <img key="dummyImg3" className="slotImage size-28 scale-75 md:size-40 md:scale-[.65]" src={images.image3} />,
    <img key="dummyImg4" className="slotImage size-28 scale-75 md:size-40 md:scale-[.65]" src={images.image4} />,
    <img key="dummyImg5" className="slotImage size-28 scale-75 md:size-40 md:scale-[.65]" src={images.image5} />,
    <img key="dummyImg6" className="slotImage size-28 scale-75 md:size-40 md:scale-[.65]" src={images.image6} />,
];

// State machine states for slot game flow
const SLOT_STATES = {
    IDLE: "idle",
    SPINNING: "spinning",
    SHOWING_RESULT: "showing_result",
    SHOWING_WIN: "showing_win",
};

export default function Slots({ SLOTS_MAX_BET }) {
    const [betAmount, setBetAmount] = useState(0);
    const [wonAmount, setWonAmount] = useState(0);
    const [showWonText, setShowWonText] = useState(false);
    const [wonMultiplier, setWonMultiplier] = useState(0);
    const [isSpinning, setIsSpinning] = useState(false);
    const [slotImages, setSlotImages] = useState([
        <img key="image1" className="slotImage size-28 scale-75 md:size-40 md:scale-[.65]" src={images.image1} />,
        <img key="image2" className="slotImage size-28 scale-75 md:size-40 md:scale-[.65]" src={images.image2} />,
        <img key="image3" className="slotImage size-28 scale-75 md:size-40 md:scale-[.65]" src={images.image3} />,
    ]);

    // State machine for managing slot game flow
    const [gameState, setGameState] = useState(SLOT_STATES.IDLE);
    const [spinResult, setSpinResult] = useState(null);
    const [lastBetAmount, setLastBetAmount] = useState(0);

    const queryClient = useQueryClient();
    const { data: currentUser } = useFetchCurrentUser();
    const { width, height } = useWindowDimensions();
    const slotsRef = useRef(null);

    useLayoutEffect(() => {
        slotsRef.current?.refreshStyles();
    }, [width, height]);

    // State machine effect to handle slot game timing
    useEffect(() => {
        let timers = [];

        const addTimer = (callback, delay) => {
            const timerId = setTimeout(callback, delay);
            timers.push(timerId);
            return timerId;
        };

        const clearAllTimers = () => {
            timers.forEach(clearTimeout);
            timers = [];
        };

        switch (gameState) {
            case SLOT_STATES.SPINNING:
                // Set spinning state
                setIsSpinning(true);

                // Update slot images immediately for better UX
                if (spinResult) {
                    addTimer(() => {
                        if (spinResult.won) {
                            const multiplierImage = "image" + (spinResult.multiplier - 1);
                            const multiplierImageArray = Array(3).fill(
                                <img
                                    className="slotImage size-28 scale-75 md:size-40 md:scale-[.65]"
                                    src={images[multiplierImage]}
                                    alt=""
                                />
                            );
                            setSlotImages(multiplierImageArray);
                        } else {
                            const randomImg = generateRandomUniqueImages(6);
                            const slotImgs = randomImg.map((imgIndex, index) => (
                                <img
                                    key={index}
                                    className="slotImage size-28 scale-75 md:size-40 md:scale-[.65]"
                                    src={images[imgIndex]}
                                    alt=""
                                />
                            ));
                            setSlotImages(slotImgs);
                        }
                    }, 500);
                }

                // Transition to showing result after spin animation
                addTimer(() => {
                    setIsSpinning(false);
                    setGameState(SLOT_STATES.SHOWING_RESULT);

                    // Invalidate user queries to update cash
                    queryClient.invalidateQueries({
                        queryKey: APIROUTES.USER.CURRENTUSERINFO,
                    });
                }, 3000);
                break;

            case SLOT_STATES.SHOWING_RESULT:
                if (spinResult?.won) {
                    // Calculate won amount
                    const calculatedWonAmount = lastBetAmount * spinResult.multiplier;

                    // Set won amount immediately if no previous win text is showing
                    if (!showWonText) {
                        setWonAmount(calculatedWonAmount);
                    } else {
                        // Delay slightly if previous win text is still showing
                        addTimer(() => setWonAmount(calculatedWonAmount), 500);
                    }

                    // Show win text and multiplier
                    setShowWonText(true);
                    setWonMultiplier(spinResult.multiplier);
                    setGameState(SLOT_STATES.SHOWING_WIN);
                } else {
                    // No win, return to idle
                    setGameState(SLOT_STATES.IDLE);
                }
                break;

            case SLOT_STATES.SHOWING_WIN:
                // Hide win text after 3 seconds
                addTimer(() => {
                    setShowWonText(false);
                    setWonMultiplier(0);
                    setGameState(SLOT_STATES.IDLE);
                }, 3000);
                break;

            case SLOT_STATES.IDLE:
            default:
                // Reset spin result when returning to idle
                setSpinResult(null);
                break;
        }

        // Cleanup function to clear all timers
        return clearAllTimers;
    }, [gameState, spinResult, lastBetAmount, showWonText, queryClient]);

    const spinSlots = useMutation(
        api.casino.gamble.mutationOptions({
            onSuccess: (data) => {
                setBetAmount(0);
                setLastBetAmount(betAmount);
                setSpinResult(data);
                setGameState(SLOT_STATES.SPINNING);

                // Start slot animation
                slotsRef.current?.startAnimation({
                    duration: 3,
                });
            },
            onError: (error) => {
                const errorMessage = error.message || "Unknown error occurred";
                console.error(errorMessage);
                toast.error(errorMessage);
                setGameState(SLOT_STATES.IDLE);
            },
        })
    );

    const handleSpin = () => {
        if (!betAmount) return;
        if (betAmount > SLOTS_MAX_BET) {
            toast.error(`The max bet is ¥${SLOTS_MAX_BET}!`);
            return;
        }
        if (betAmount < 5) {
            toast.error(`The min bet is ¥5!`);
            return;
        }
        if (betAmount > currentUser?.cash) {
            toast.error("You don't have enough cash to bet that much!");
            return;
        }
        setShowWonText(false);

        spinSlots.mutate({ amount: betAmount });
    };

    const allIn = () => {
        setBetAmount(currentUser?.cash);
    };

    return (
        <div>
            <p className="text-center font-accent text-3xl xl:mr-24">Slots</p>
            <div className="relative mx-auto flex flex-col rounded-lg p-3">
                <div className="md:-translate-x-20 mx-auto flex gap-6 md:h-[162px]">
                    <div className="hidden h-fit w-52 flex-col gap-2 rounded-lg border-2 border-slate-600 bg-slate-200 pb-1 shadow-lg md:flex dark:bg-slate-800">
                        <p className="w-full rounded-t-md border-gray-600 border-b bg-gray-200 py-1 text-center font-accent dark:bg-gray-900 dark:text-stroke-md">
                            Multipliers
                        </p>
                        <div className="flex-col gap-2 px-1 md:flex">
                            <div
                                className={cn(
                                    "flex justify-center rounded-md py-0.5",
                                    wonMultiplier === 2 && "bg-green-400 shadow-lg"
                                )}
                            >
                                <div className="flex w-3/4">
                                    <img className="w-[33.33%]" src={images.image1} alt="" />
                                    <img className="w-[33.33%]" src={images.image1} alt="" />
                                    <img className="w-[33.33%]" src={images.image1} alt="" />
                                </div>
                                <p className="my-auto ml-1 text-green-500 text-stroke-sm">= 2x</p>
                            </div>
                            <div
                                className={cn(
                                    "flex justify-center rounded-md py-0.5",
                                    wonMultiplier === 3 && "bg-green-400 shadow-lg"
                                )}
                            >
                                <div className="flex w-3/4">
                                    <img className="w-[33.33%]" src={images.image2} alt="" />
                                    <img className="w-[33.33%]" src={images.image2} alt="" />
                                    <img className="w-[33.33%]" src={images.image2} alt="" />
                                </div>

                                <p className="my-auto ml-1 text-blue-500 text-stroke-sm"> = 3x</p>
                            </div>
                            <div
                                className={cn(
                                    "flex justify-center rounded-md py-0.5",
                                    wonMultiplier === 4 && "bg-green-400 shadow-lg"
                                )}
                            >
                                <div className="flex w-3/4">
                                    <img className="w-[33.33%]" src={images.image3} alt="" />
                                    <img className="w-[33.33%]" src={images.image3} alt="" />
                                    <img className="w-[33.33%]" src={images.image3} alt="" />
                                </div>
                                <p className="my-auto ml-1 text-amber-500 text-stroke-sm"> = 4x</p>
                            </div>
                        </div>
                    </div>
                    <SlotCounter
                        ref={slotsRef}
                        containerClassName="border-2 border-slate-600 shadow-lg rounded-xl divide-x-2 dark:divide-slate-600 divide-slate-600 dark:bg-slate-800 bg-slate-200 "
                        autoAnimationStart={false}
                        duration={0}
                        dummyCharacterCount={60}
                        value={slotImages}
                        dummyCharacters={dummyCharacters}
                    />
                </div>

                <div className="mx-auto mt-5 flex w-fit flex-col justify-center rounded-md border border-slate-600 px-2 py-3 md:px-8 dark:bg-slate-800">
                    <div className="flex flex-row">
                        <div className="flex flex-col">
                            <label
                                htmlFor="betAmount"
                                className="mb-0.5 block w-28 font-body font-semibold text-gray-900 text-sm dark:text-white"
                            >
                                BET AMOUNT
                            </label>
                            <div className="relative">
                                <input
                                    id="betAmount"
                                    name="betAmount"
                                    type="number"
                                    className="mr-5 block h-12 w-32 rounded-md border border-gray-300 bg-gray-50 p-2.5 pl-10 text-center text-xl shadow-lg focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-slate-200 dark:text-stroke-sm dark:focus:border-blue-500 dark:focus:ring-blue-500 dark:placeholder:text-gray-400"
                                    placeholder="0"
                                    min={0}
                                    max={SLOTS_MAX_BET}
                                    value={betAmount}
                                    onChange={(e) => setBetAmount(parseInt(e.target.value))}
                                />
                                <div className="-translate-y-1/2 absolute top-1/2 flex h-full w-8 rounded-l-md border bg-slate-800 text-stroke-sm dark:border-gray-600">
                                    <p className="m-auto text-gray-300 text-xl">¥</p>
                                </div>
                            </div>
                        </div>{" "}
                        <Button
                            height="h-10!"
                            rounded="rounded-lg"
                            className="mt-6 w-24 font-bold font-display text-xl"
                            type="secondary"
                            disabled={isSpinning}
                            onClick={() => allIn()}
                        >
                            All In!
                        </Button>
                    </div>

                    <Button
                        height="h-12!"
                        rounded="rounded-lg"
                        className="mx-auto mt-4 w-40 font-bold font-display text-xl"
                        type="tertiary"
                        disabled={isSpinning || !betAmount}
                        onClick={() => handleSpin()}
                    >
                        SPIN!
                    </Button>
                </div>

                <div className="mx-auto mt-4 flex w-1/2 flex-col gap-2 rounded-lg border-2 border-slate-600 bg-slate-200 py-2 shadow-lg md:hidden dark:bg-slate-800">
                    <div className="flex justify-center">
                        <div className="flex w-3/5">
                            <img className="w-[33.33%]" src={images.image1} alt="" />
                            <img className="w-[33.33%]" src={images.image1} alt="" />
                            <img className="w-[33.33%]" src={images.image1} alt="" />
                        </div>
                        <p className="my-auto ml-1 text-green-500 text-stroke-sm">= 2x</p>
                    </div>
                    <div className="flex justify-center">
                        <div className="flex w-3/5">
                            <img className="w-[33.33%]" src={images.image2} alt="" />
                            <img className="w-[33.33%]" src={images.image2} alt="" />
                            <img className="w-[33.33%]" src={images.image2} alt="" />
                        </div>

                        <p className="my-auto ml-1 text-blue-500 text-stroke-sm"> = 3x</p>
                    </div>
                    <div className="flex justify-center">
                        <div className="flex w-3/5">
                            <img className="w-[33.33%]" src={images.image3} alt="" />
                            <img className="w-[33.33%]" src={images.image3} alt="" />
                            <img className="w-[33.33%]" src={images.image3} alt="" />
                        </div>
                        <p className="my-auto ml-1 text-amber-500 text-stroke-sm"> = 4x</p>
                    </div>
                </div>
                <p
                    className={cn(
                        "z-10 mt-6 text-center text-4xl dark:text-stroke-md",
                        showWonText ? "fading-text-visible" : "fading-text"
                    )}
                >
                    You won <span className="w-28 text-indigo-400">¥{wonAmount}!</span>
                </p>
            </div>
        </div>
    );
}
